<template>
    <div class="home border-box" style="background-color: #f2f5f8">
        <div class="all-padding-16">
            <div class="display-flex t-margin-32 gap-8">
                <img src="@/assets/hub-images/my-touxiang.png" alt="" width="80" />
                <div class="display-flex left-right-center flex-column l-margin-8 color-white gap-4">
                    <span class="font-24 font-weight-600">{{ userInfo.nickname }}</span>
                    <span class="font-16">{{ userInfo.username }}</span>
                </div>
            </div>
            <div class="t-margin-48 display-flex border-radius-8 all-padding-16" style="background-color: #ffffff">
                <div class="display-flex flex-column gap-24">
                    <span class="font-16 font-weight-600 color-black">我的权益</span>
                    <div class="display-flex space-between" style="width: 8rem">
                        <div class="flex-center flex-column gap-8" style="width: 4rem" @click="jumpToBenefit('xs')">
                            <span class="font-18 color-black font-weight-600">{{ xsAccont }} </span>
                            <span class="font-14 color-two-grey">线索数量</span>
                        </div>
                        <div style="background-color: #b3b3b3; width: 1px; height: 100%; margin: 0 16px"></div>
                        <div class="flex-center flex-column gap-8" style="width: 4rem" @click="jumpToBenefit('bg')">
                            <span class="font-18 color-black font-weight-600">{{ reportCount }}</span>
                            <span class="font-14 color-two-grey">报告数量</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="t-margin-12 display-flex border-radius-8 all-padding-16" style="background-color: #ffffff">
                <div class="display-flex top-bottom-center space-between" style="width: 9rem" @click="jumpToHelp">
                    <div class="flex-center gap-8">
                        <Icon icon="icon-a-zhuanligaikuang" size="24" />
                        <span class="font-18">客服咨询</span>
                    </div>
                    <van-icon name="arrow" color="#B3B3B3" size="0.5rem" />
                </div>
            </div>
            <div class="t-margin-12 display-flex border-radius-8 all-padding-16" style="background-color: #ffffff">
                <div class="flex-center" style="width: 9rem">
                    <span class="font-18 color-red" @click="handleLogout">退出登录</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import orderService from '@/service/orderService'
import Icon from '@/components/common/Icon.vue'
import { useRouter } from 'vue-router'
import { showConfirmDialog } from 'vant'

const fpbgAccont = ref()
const xsAccont = ref(0)
const swbgAccont = ref(0)
const gqbgAccont = ref(0)
//计算报告数量总和
const reportCount = computed(() => {
    return fpbgAccont.value + swbgAccont.value + gqbgAccont.value || 0
})
const store = useStore<RootState>()
const user = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    return user
})

const userInfo = computed(() => {
    return {
        nickname: user.value?.nickname || '未登录',
        username: user.value?.username || '-',
    }
})
const router = useRouter()
const jumpToHelp = () => {
    router.push({
        name: 'customerHelp',
    })
}
const jumpToBenefit = (val: string) => {
    console.log('jumpToBenefit', val)
    router.push({
        name: 'my-benefit',
        query: {
            type: val,
        },
    })
}
const logout = async () => {
    store.dispatch('auth/logout')
}

const handleLogout = () => {
    console.log('退出登录')
    showConfirmDialog({
        title: '提示',
        message: '确定退出登录吗？',
    }).then(() => {
        logout()
    })
}

const getOrderStatistics = () => {
    orderService.orderServiceStatistics({ serviceKeys: 'fpbg,xs,swbg,gqbg' }).then((res) => {
        console.log('res', res)
        res.filter((item) => item.label === 'xs').forEach((item) => {
            xsAccont.value = item.num
        })
        res.filter((item) => item.label === 'fpbg').forEach((item) => {
            fpbgAccont.value = item.num
        })
        res.filter((item) => item.label === 'swbg').forEach((item) => {
            swbgAccont.value = item.num
        })
        res.filter((item) => item.label === 'gqbg').forEach((item) => {
            gqbgAccont.value = item.num
        })
    })
}

onMounted(() => {
    getOrderStatistics()
})
</script>

<style lang="scss" scoped>
.home {
    height: 100%;
    overflow: scroll;
    display: flex;
    flex-direction: column;
    background-image: url('@/assets/hub-images/my-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
</style>
