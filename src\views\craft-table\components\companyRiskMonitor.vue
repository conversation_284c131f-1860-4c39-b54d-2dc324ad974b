<template>
    <div class="display-flex space-between" width="100%">
        <div v-for="item in dataList" :key="item.id" @click="jumpTo()">
            <div class="display-flex flex-column gap-12 w-100 border-radius-4 all-padding-4 " :style="{'background': item.bgcolor, 'color': item.color}">
                <div class="display-flex top-bottom-center">
                    <Icon icon="icon-fnegxian" :color="item.color" />
                    <div class="font-14 font-weight-600" >{{ item.label }}</div>
                </div>
                <div class="font-20 font-weight-600" >{{ item.num }}</div>
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref,onMounted, computed } from 'vue'
import Icon from '@/components/common/Icon.vue'
import crmService from '@/service/crmService'

const dataList = computed(() => 
    [
        {
            id:'1',
            num: entRiskData.value[0],
            label: '低风险',
            color:'#24B395',
            bgcolor:'linear-gradient(to bottom, #E1FFFF, #FAFFFE)'
        },
        {
            id:'2',
            num: entRiskData.value[1],
            label: '中风险',
            color:'#FFC043',
            bgcolor:'linear-gradient(to bottom, #FFF7E8, #FFFEFB)'
        },
        {
            id:'3',
            num: entRiskData.value[2],
            label: '高风险',
            color:'#FF5151',
            bgcolor:'linear-gradient(to bottom, #FFE6E5, #FFFCFB)'
        },
    ]
)

const jumpTo = () => {
    console.log('jump to')
}
const entRiskData = ref([0,0,0])
// 风险统计
const getEntRisk =async () => {
    const res = await crmService.statisticsEntRiskMonitor()
    entRiskData.value[0] = res.data.filter(item => item.label === '低风险')[0].count
    entRiskData.value[1] = res.data.filter(item => item.label === '中风险')[0].count
    entRiskData.value[2] = res.data.filter(item => item.label === '高风险')[0].count
}

onMounted(() => {
    getEntRisk()
})


</script>

<style lang='scss' scoped>
</style>