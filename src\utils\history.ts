import { getItem, removeItem, setItem } from './storage'
import { _store } from '@/store'

export const getSearchHistory = () => {
    const { account } = _store.state.user
    const { user } = account || {}
    const { id } = user || {}

    const history = getItem('search_history_' + id)
    return history ? JSON.parse(history) : []
}

export const setSearchHistory = (value: string) => {
    const { account } = _store.state.user
    const { user } = account || {}
    const { id } = user || {}

    const history: string[] = getSearchHistory()

    const filterHistory = history.filter((item) => item !== value)

    filterHistory.unshift(value)

    setItem('search_history_' + id, JSON.stringify(filterHistory))
}

export const clearSearchHistory = () => {
    const { account } = _store.state.user
    const { user } = account || {}
    const { id } = user || {}

    removeItem('search_history_' + id)
}
