import type { IPaginationResponse, ICommonResponse } from './axios'
import type { IAllRecord } from './record'

import type { ISearchConditions } from '@/types/model'

export interface ISearchItemType {
    key: string
    type: 'input' | 'select' | 'multipleSelect' | 'date' | 'cascader' | 'slider' | 'checkAllMultipleSelect'
    placeholder?: string
    label: string
    editable: boolean
    isShow: boolean
    options?: Array<{
        label: string
        value: number | string | boolean
    }>
    props?: {
        label?: string
        value?: string
        emitPath?: boolean
        checkStrictly?: boolean
    }
    isIndeterminate?: boolean
    checkAll?: boolean
}

export interface ILeadColumn {
    id: number
    type: string
    label: string
    width?: string
    key: string
    prop: string
    editable: boolean
    fixed?: string
    sortable?: boolean
    isShow?: true
}
export interface IGetCrmLeadParams extends IAllRecord {
    clueId?: string // 线索池
    page: number
    pageSize: number
    areaCode?: string //区域
    basicScore?: number //基础得分
    beforeUser?: string //前负责人
    channel?: string //线索渠道
    clueName?: string //线索名称
    companyName?: string //企业名称
    createDateEnd?: string //创建时间-结束时间
    createDateStart?: string //创建时间-开始时间
    createUser?: string //创建人
    importBatchIds?: string //导入批次ID
    invoiceCollectDateEnd?: string //发票采集时间-结束时间
    invoiceCollectDateStart?: string //发票采集时间-开始时间
    newFollowDateEnd?: string //最新跟进时间-结束时间
    newFollowDateStart?: string //最新跟进时间-开始时间
    note?: string //备注
    outBoundResult?: string //外呼结果
    source?: string //数据来源
    sourceCompanyName?: string //来源企业（参数待定）
    status?: string //跟进状态
    tagIds?: string //标签
    taxCollectDateEnd?: string //税务采集时间-结束时间
    taxCollectDateStart?: string //税务采集时间-开始时间
    user?: string //负责人
    ids?: string
    // searchTag?: number //1:线索池列表 2:线索列表-全部线索 3:线索列表-我的线索
    searchTag?: number //1:线索列表-我的线索 2:客户列表-我的客户 3:客户列表-我协作的客户 4:客户公海
    isH5?: boolean
    socialCreditCode?: string
}

type FileItem = {
    name: string
    url: string
}
export interface ILeadData {
    id: string
    companyName: string
    socialCreditCode: string
    rateLevel: string
    riskLevel: number
    basicScore: number
    user: string
    contactInfo: {
        mobile?: string
    }
    outBoundResult: string
    createTime: string
    nextFollowDate: string
    newFollowDate: string
    status: number
    source: number
    crmTagIds: string[]
    note: string
    invoiceCollectDate: number
    taxCollectDate: number
    sourceCompany: string
    beforeUser: string
    channel: string
    newFollowDescription?: string
    isCustomer?: string
    name: string
    customFields?: IAllRecord
    departmentName?: string
    allContacts?: { contact: string; position: string; content: string }[]
    statusStr: string
    sourceStr: string
    createUser: string
    companyId: string
    ministrantInfos: { nickname: string; id: string }[]
    isBuy?: boolean
    updateTime: number
    turnCustomerDate: number
    poolId: string
    releationLeadList?: ILeadData[]
    supplementContacts?: { contact: string; note: string; content: string }[]
    newFollowFiles?: FileItem[]
    newFollowImg?: FileItem[]
    lastDepartmentName?: string
    tagInfos: ITagInfo[]
    healthScore?: number
    isDemonstrateData?: string
    operationStatusLabel?: string
}

export interface IGetLeadResponse {
    errCode: number
    success: boolean
    page: number
    pageSize: number
    total: number
    totalPages?: number
    data?: ILeadData[]
}

export interface ICustomerTabPoolParams extends IAllRecord {
    isCustomerPool?: number // 1 获取客户公海顶部pool
}

type userLIst = {
    nickname: string
    id: string
}
export interface IGetTabPool {
    id: string
    name: string
    sort: number
    user: userLIst[]
}
export interface IPageInfo extends IAllRecord {
    page: number
    pageSize: number
}

export interface IGetTagResponse extends IPaginationResponse {
    data: IGetTagList[]
}
export interface IGetTagList {
    color: string
    createDate: string
    del: number
    id: string
    level: string
    orgId: string
    tagName: string
    tanantId: string
    userId: string
}

export interface IDelLeadParams extends IAllRecord {
    ids: string
}

export interface IDelLeadResponse extends ICommonResponse {
    data?: []
}

export interface ILeadTransfelParams {
    ids: string[] //线索ID
    customerPoolId?: string //如果是从客户公海转出，需要传参对应的客户公海ID
    fromCustomerOpenSea?: boolean //是否是从客户公海转出标识 属于3
    orgId?: string //组织ID
    targetPoolId?: string //转移给池子时的目标池子ID
    targetUserId?: string //转移给人时的目标人ID
    transferType?: number // 1：人到池子 2：人到人 3：池子到人 4：池子到池子 5:转移为新客户 6：转移为已有客户
    customerId?: string
}

export interface ICrmAddParams {
    companyName: string
    socialCreditCode: string
    phone?: string
    channel?: string
    remark?: string
    clueType: number
    source: number // 线索来源 1:找企业 6:上下游 9:其他 10:导入 13:企业经营慧眼系统 14:新增
    contactInfo?: string
    contactType?: number
}
export interface IUpdateMinistrantParams {
    leadIds: string[]
    addUser?: string[] | []
    delUser?: string[]
    replaceFrom?: string
    replaceTo?: string
    replaceUser?: string[]
    replaceType?: number
}

export interface ICrmGetActiviesResponse extends IPaginationResponse {
    data: ICrmGetActiviesItem[]
}

export interface ICrmGetActiviesParams extends IAllRecord {
    leadId: string
    activityType?: string
    createTime?: number[]
    page: number
    pageSize: number
}

export interface CacheData {
    date: string
    data: ICrmGetActiviesItem
}

export interface ICrmGetActiviesItem {
    leadId?: string
    customerId?: string
    name?: string
    username?: string
    description: string
    followType?: string
    activityType: string
    referName: string
    referType: string
    followImg?: FollowImg[]
    followFiles?: FollowFiles[]
    nextFollowDate?: string
    realFollowDate?: string
    createTime: string
    saleableName?: string
    saleableType?: string
    outboundResult?: {
        result: string
        taskCode: string
        name: string
        callPhone: string
        aiTag?: string
    }
}

export interface FollowImg {
    url?: string
    name: string
    path?: string
    fileID?: string
    originalName?: string
}

export interface FollowFiles {
    url?: string
    name: string
    fileID?: string
    originalName: string
}

export interface IUpdateActiviesParams {
    activityType: string
    leadId: string
    followType?: string
    realFollowDate?: number
    description?: string
    followFiles?: FollowFiles[]
    followImg?: FollowImg[]
    nextFollowDate?: number
}

export interface ISearchPoolListParams extends IAllRecord {
    type: string
    page: number
    pageSize: number
}

export interface SearchPoolListResponse extends IPaginationResponse {
    data: SearchPoolListResponseItem[]
}

export interface SearchPoolListResponseItem {
    poolId: string
    id: string
    managers?: string[]
    name: string
    openReback: number | null
    rebackRules?: ReBackRules[]
    openGetrebackRule?: number | null
    getbackRules?: GetrebackRule[]
    sort?: number
    users: Users[]
    isYxtz?: boolean
}

export interface SetRuleParams {
    poolId: string
    openReback?: number | null
    rebackRules?: ReBackRules[]
    type: number
    sort?: number
    openGetrebackRule?: number | null
    getbackRules?: GetrebackRule[]
}

export interface ReBackRules {
    dayNum: number
    type: string
}

export interface GetrebackRule {
    users: string[]
    num: number
    allUser: boolean
}
export interface Users {
    id: string
    mobile?: string
    username?: string
    nickname?: string
}

export interface IAddPoolParams {
    poolId?: string
    name: string
    type: string
    userIds: Users[]
}

export interface IAddAndDeleteResponse extends ICommonResponse {
    data: object
}

export interface ILeadPoolRequest extends IAllRecord {
    page: number
    pageSize: number
    type: string
}

export interface ILeadPoolResponse extends IPaginationResponse {
    data: ILeadPool[]
}

export interface ILeadPool {
    id: string
    name: string
    sort: number
    openReback: number
    rebackRules: string[]
    managers: string[]
    users: string[]
}

export interface IUpdateContactsResponse extends ICommonResponse {
    data: object
}

export interface IUpdateContactsRequest {
    socialCreditCode: string
    name: string
    crmId: string
}

interface FileUploadItem {
    link: string
    domain: string
    name: string
    originalName: string
    attachId?: string
}

export interface FileUploadResponse extends ICommonResponse {
    data: FileUploadItem
}

export interface FileIns {
    response: FileUploadResponse
    name: string
    status: string
    uid: number
}

export interface ICrmSendSmsParams {
    leadIds?: string[]
    smsModelId?: ''
}

export interface ICrmAddFromListCompany {
    companyName: string
    socialCreditCode: string
}

export interface ICrmAddFromListRequest {
    companyInfos: ICrmAddFromListCompany[]
    transferTo: string
    transferType: string
    poolId?: string
}

export interface ICrmUpdateParams {
    leadId: string
    companyName?: string
    socialCreditCode?: string
    phone?: string
    channel?: string
    remark?: string
    clueType?: number
    source?: number // 线索来源 1:找企业 6:上下游 9:其他 10:导入 13:企业经营慧眼系统 14:新增
    contactInfo?: string
    contactType?: number
    supplementContacts?: { contact: string; content: string; note: string }[]
    crmTagIds?: string[]
    updateTags?: boolean
}

export interface ICrmExportParams {
    areaCode?: string
    basicScore?: string[]
    beforeUser?: string
    channel?: string
    clueId?: string
    companyName?: string
    createDate?: string[]
    createUser?: string
    customerPoolName?: string
    exportNum?: number
    exportType?: number
    ids?: string[]
    importBatchIds?: string[]
    invoiceCollectDate?: string[]
    ministrantInfo?: string[]
    name?: string
    newFollowDate?: string[]
    note?: string
    outBoundResult?: number
    page?: number
    pageSize?: number
    searchTag?: number
    socialCreditCode?: string
    source?: number
    sourceCompanyName?: string
    status?: number
    tagIds?: string[]
    taxCollectDate?: string[]
    turnCustomerPoolDate?: string[]
    user?: string
}

export interface ICrmImportParams {
    file: string
    type: '1' | '2' // 1:线索 2:客户
}

export interface IAddTagParams {
    tagName: string
    color: string
}
export interface IUpdateTagParams {
    id: string
    tagName: string
    color: string
}
export interface CrmGoodsPolicyListParams extends IAllRecord {
    issuingDepartment?: string
    issuingTime?: string[]
    page: number
    pageSize: number
    policyLevel?: string
    policyName?: string
    policyTopic?: string
}

export interface CrmGoodsPolicyListItem {
    id: string
    policyName: string
    policyTopic: string
    policyLevel: string
    issuingDepartment: string
    issuingTime: string
    rules: {
        list: ISearchConditions[]
    }
}
export interface CrmGoodsPolicyListResponse {
    data: CrmGoodsPolicyListItem[]
    page: number
    pageSize: number
    total: number
    totalPages: number
}

export interface ITagInfo {
    id: string
    tagName: string
    color: string
    isShowIcon?: boolean
}

export interface IRuleUpdateParams {
    id: string
    rules: {
        list: ISearchConditions[]
    }
}

export interface CrmGoodsPolicyEnumDataResponse {
    policyIndustryEnum: { [key: string]: string }
    policyIssuingDepartmentEnum: { [key: string]: string }
    policyLevelEnum: { [key: string]: string }
    policyTopicTypeEnum: { [key: string]: string }
    policyTypeEnum: { [key: string]: string }
}

export interface CrmGoodsFinanceEnumDataResponse {
    financialProduct: { [key: string]: string }
}

export interface ITagCountResponse extends ICommonResponse {
    data: ITagCountResponseData[]
}

export interface ITagCountResponseData {
    name: string
    num: number
    id?: string
    member?: string[]
    type?: number
}

export interface ITagAddResponse extends ICommonResponse {
    data: ITagAddResponseItem
}

export interface ITagAddResponseItem {
    id: string
    color: string
    tagName: string
}

// export interface GoodsFinanceEntMatchRuleRulesChildren {
//     matchingLabel: string
//     level: number
//     dataType: string
//     type: string
//     valueTypeLabel: string
//     operator: string
//     parentId: number
//     matching: string
//     propLabel: string
//     valueLabel: string
//     isTop: boolean
//     operatorLabel: string
//     prop: string
//     valueType: string
//     id: number
//     value: number
//     maxScore: number
// }

export interface IShowListItem {
    propLabel: string
}

export interface IGoodsPolicyItem {
    name: string
    childGoodsType: number | string
    childGoodsTypeStr?: string
    spu: {
        area: string
        declareCondition: string
        declareEndTime: number
        declareStartTime: number
        declareUrl: string
        description: string
        incentives: string
        interpretation: string
        issuingTime: 0
        policyContent: string
        policyIndustry: string
        issuingDepartment: string
        policyLevel: string
        policyNumber: string
        policyPoint: string
        policySource: string
        policyTopic: string
        deadlineTime: string
        policyTopicStr?: string
        policyLevelStr?: string
        policyIndustryStr?: string
        issuingDepartmentStr?: string
    }
    id?: string
    matchScore?: MatchCompanyItem
    rules?: {
        list: ISearchConditions[]
    }
    flatRules?: {
        id: string
        prop: string
        maxScore: number
        matching: string
        value: string | number
        operator: string
        data_type: string
        propLabel: string
    }[]
    totalScore?: number
}

export interface IGoodsProductItem {
    name: string
    childGoodsType: number
    spu: {
        moneyLimits: string
        rateDown: string
        rateUpper: string
        loanCycle: string
        refundWay: string
        rebateExplanation?: string
    }
    sellAreaNames: {
        all: string[]
        cities: string[]
    }
    notSellIndustryNames: string
    goodsName: string
    sellPoint: string
    id?: string
    matchScore: MatchCompanyItem
    rules?: {
        list: ISearchConditions[]
    }
    flatRules?: {
        id: string
        prop: string
        maxScore: number
        matching: string
        value: string | number
        operator: string
        data_type: string
        propLabel: string
    }[]
    totalScore?: number
}

export interface CrmGoodsFinanceListParams extends IAllRecord {
    financialName?: string
}

export interface CrmGoodsFinnanceListResponse {
    data: IGoodsProductItem[]
    page: number
    pageSize: number
    total: number
    totalPages: number
}

export interface MatchCompanyItem {
    name: string
    flatRules: {
        id: string
        prop: string
        maxScore: number
        matching: string
        value: string | number
        operator: string
        data_type: string
        propLabel: string
    }[]
    totalScore: number
    [key: string]: number | string | object
}
export interface GoodsFinanceEntMatchRuleItem {
    id: string
    name: string
    spu: {
        area: string
        policyPoint: string
        declareUrl: string
        policyIndustry: string
        attachment: {
            name: string
            url: string
        }
        interpretation: string
        policyType: number
        policyTopic: string
        issuingDepartment: string

        moneyLimits?: string
        rateDown?: number
        rateUpper?: number
        loanCycle?: string
    }
    rules: {
        list: Array<Record<string, unknown>>
    }
    matchScore: MatchCompanyItem
    sellPoint?: string
    showList?: IShowListItem[]
    banner_imags?: string
}
export interface IGoodsFinanceEntMatchRuleResponse extends ICommonResponse {
    data: GoodsFinanceEntMatchRuleItem[]
}

export interface IGoodsFinanceMatchDetailParams extends IAllRecord {
    companyId: string
    id: string
}

export interface IGoodsPolicyTransferReauest {
    leadId: string
    productId: string
}

export interface ICrmImportBatchEnumParams extends IAllRecord {
    isAll: boolean
    type: number // 1线索 2客户
}

export interface ICrmImportBatchEnumItem {
    createUser: string
    id: string
    importDate: number
    importDateStr: string
    orgId: string
    schemaName: string
    successNum: number
    tenantId: string
    totalNum: number
}
export interface ICrmImportBatchEnumResponse extends ICommonResponse {
    data: ICrmImportBatchEnumItem[]
}

export interface GsGetCompanyClueInfoResponse extends ICommonResponse {
    data: {
        clueType: number
        customerPoolName: string
        id: string
        leadPoolName: string
        username: string
    }
}

export interface ICrmRiskRiskListParams extends IAllRecord {
    page: number,
    pageSize: number
    companyName?: string,
    socialCreditCode?: string,
    riskLevel?: string,
    source?: string,
    updateTime?:number[] 
}

export interface IRiskTypeOverViewItem {
    label: string
    num: number
}
export interface IRiskListItem {
    id: string
    name: string
    companyName: string
    socialCreditCode: string
    riskLevel: number
    source: number
    updateTime: number
    riskTypeOverView: IRiskTypeOverViewItem[],
    operationStatusLabel?: string
}
export interface ICrmRiskRiskListResponse extends IPaginationResponse{
    data: IRiskListItem[]
}
