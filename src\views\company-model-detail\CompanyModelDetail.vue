<template>
    <div v-if="socialCreditCode && modelName">
        <component :is="RenderComponent" :socialCreditCode="socialCreditCode" :modelName="modelName" />
    </div>
    <div v-else>参数不能为空</div>
</template>
<script setup lang="ts">
import List from './components/List.vue'
import { useRoute } from 'vue-router'
import Info from './components/Info.vue'
import { computed } from 'vue'
import { PageConfig } from './config'
const route = useRoute()
const socialCreditCode = route.query.socialCreditCode as string
const modelName = route.query.modelName as keyof typeof PageConfig

const RenderComponent = computed(() => {
    const compoennt = PageConfig[modelName]?.component
    if (compoennt === 'Info') {
        return Info
    }
    if (compoennt === 'List') {
        return List
    }
    return null
})
</script>
