/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BasicSearchItem: typeof import('./src/components/enterprise/list/BasicSearchItem.vue')['default']
    Cascader: typeof import('./src/components/enterprise/ui/Cascader.vue')['default']
    Checkbox: typeof import('./src/components/enterprise/ui/Checkbox.vue')['default']
    CheckboxBtn: typeof import('./src/components/enterprise/ui/CheckboxBtn.vue')['default']
    CollectPopup: typeof import('./src/components/collect/CollectPopup.vue')['default']
    CompanySearchList: typeof import('./src/components/enterprise/list/CompanySearchList.vue')['default']
    CountDownTimer: typeof import('./src/components/count-down-timer/CountDownTimer.vue')['default']
    Icon: typeof import('./src/components/common/Icon.vue')['default']
    MappedCascadeSelect: typeof import('./src/components/enterprise/region/MappedCascadeSelect.vue')['default']
    MappedFilter: typeof import('./src/components/enterprise/filters/MappedFilter.vue')['default']
    MultiProvinceFilter: typeof import('./src/components/enterprise/filters/MultiProvinceFilter.vue')['default']
    MultiSelectFilter: typeof import('./src/components/enterprise/filters/MultiSelectFilter.vue')['default']
    PhysicalExaminationSummary: typeof import('./src/components/physical-examination-summary/PhysicalExaminationSummary.vue')['default']
    PolicyMatch: typeof import('./src/components/policy-match/PolicyMatch.vue')['default']
    ProductMatch: typeof import('./src/components/product-match/ProductMatch.vue')['default']
    RegionCascadeSelect: typeof import('./src/components/enterprise/region/RegionCascadeSelect.vue')['default']
    RegionFilter: typeof import('./src/components/enterprise/filters/RegionFilter.vue')['default']
    RegionMultipleSelect: typeof import('./src/components/enterprise/region/RegionMultipleSelect.vue')['default']
    RegionSelector: typeof import('./src/components/enterprise/region/RegionSelector.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SeekHelp: typeof import('./src/components/common/SeekHelp.vue')['default']
    SelectFilter: typeof import('./src/components/enterprise/filters/SelectFilter.vue')['default']
    SpecialInspection: typeof import('./src/components/special-inspection/SpecialInspection.vue')['default']
    SpecialInspectionDetail: typeof import('./src/components/special-inspection-detail/SpecialInspectionDetail.vue')['default']
    VanActionSheet: typeof import('vant/es')['ActionSheet']
    VanButton: typeof import('vant/es')['Button']
    VanCalendar: typeof import('vant/es')['Calendar']
    VanCascader: typeof import('vant/es')['Cascader']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanCheckboxGroup: typeof import('vant/es')['CheckboxGroup']
    VanCountDown: typeof import('vant/es')['CountDown']
    VanDatePicker: typeof import('vant/es')['DatePicker']
    VanDialog: typeof import('vant/es')['Dialog']
    VanDivider: typeof import('vant/es')['Divider']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanList: typeof import('vant/es')['List']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanPicker: typeof import('vant/es')['Picker']
    VanPopover: typeof import('vant/es')['Popover']
    VanPopup: typeof import('vant/es')['Popup']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanSearch: typeof import('vant/es')['Search']
    VanSkeleton: typeof import('vant/es')['Skeleton']
    VanSlider: typeof import('vant/es')['Slider']
    VanStepper: typeof import('vant/es')['Stepper']
    VanSticky: typeof import('vant/es')['Sticky']
    VanSwipe: typeof import('vant/es')['Swipe']
    VanSwipeItem: typeof import('vant/es')['SwipeItem']
    VanTab: typeof import('vant/es')['Tab']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
    VanTabs: typeof import('vant/es')['Tabs']
    VanTag: typeof import('vant/es')['Tag']
    VanUploader: typeof import('vant/es')['Uploader']
  }
}
