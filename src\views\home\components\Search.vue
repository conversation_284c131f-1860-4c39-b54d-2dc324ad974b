<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const currentkey = ref('0')
const toSearch = () => {
    router.push({ name: 'companySearch' })
}

const searchTabs = [
    {
        label: '智能搜索',
        val: '0',
        plac: '智能搜索，输入任意关键词，以空格隔开',
    },
    {
        label: '找企业',
        val: 'companyname',
        plac: '请输入企业名称',
    },
    {
        label: '找人脉',
        val: 'personnel',
        plac: '请输入一个完整姓名（支持企业法人/高管/其他员工）',
    },
]

const switchKey = (key: string) => {
    currentkey.value = key
}

const placeholder = computed(() => {
    return searchTabs.find((item) => item.val === currentkey.value)?.plac
})

const isActive = (key: string) => {
    return currentkey.value === key
}
</script>

<template>
    <div class="home-search">
        <div
            class="flex flex-column gap-12 back-color-white top-bottom-center border-radius-8 lr-padding-12 tb-padding-24"
        >
            <div
                class="flex flex-row top-bottom-center gap-32 left-right-center width-100 color-black font-weight-500 b-margin-12"
            >
                <div
                    :class="{
                        'font-16': true,
                        'color-blue': isActive(value.val),
                        'font-weight-700': isActive(value.val),
                    }"
                    v-for="(value, index) in searchTabs"
                    :key="index"
                    @click="switchKey(value.val)"
                >
                    {{ value.label }}
                </div>
            </div>
            <div class="width-100">
                <div class="flex flex-row h-48 top-bottom-center border-radius-6 search" @click.stop="toSearch">
                    <div class="flex top-bottom-center left-right-center font-16 lr-padding-4">
                        <Icon icon="icon-a-new-search" color="var(--text-grey)" size="24" />
                    </div>
                    <div class="font-12 color-text-grey lh-20 font-weight-500">{{ placeholder }}</div>
                </div>
            </div>
            <div class="width-100 flex flex-row gap-4">
                <div class="flex top-bottom-center left-right-center font-16">
                    <van-icon name="fire" class="color-red" />
                </div>
                <div class="font-13 color-black font-weight-500 flex flex-row">
                    <div>近7日：</div>
                    <div class="r-margin-12">新增 <span class="color-blue">40W+</span> 企业</div>
                    <div>新增 <span class="color-sz-warning">300W+</span> 联系方式</div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.home-search {
    margin-bottom: 16px;
    padding-left: 16px;
    padding-right: 16px;
}

.search {
    border: 1px solid var(--main-blue-);
    width: 100%;
}
</style>
