<script lang="ts" setup>
import type { IAicNormalSearchRules, INormalFilterParams, Region } from '@/types/aic'
import { cleanChildren } from '@/utils/flatten'
import type { CascaderOption } from 'vant'
import { onMounted, ref, watch } from 'vue'

interface Option {
    value: string
    label: string
    children?: Option[]
}

const fieldNames = {
    text: 'label',
    value: 'value',
    children: 'children',
}

const props = defineProps<{
    regions: Region[]
    close: () => void
    data: IAicNormalSearchRules
    storeParams: INormalFilterParams[]
    pushSelectRegion: (v: Region) => void
}>()

const cascaderValue = ref('')
const cascaderOptions = ref<Option[]>([])
const tab1Index = ref(0)
const tab2Index = ref(0)
const formatOptions = (regions: Region[]) => {
    return convertFirstLevelRegionsToOptions(regions)
}

function getFirstLetter(pinyin: string): string {
    return pinyin.charAt(0).toUpperCase()
}

function sortRegionsByPinyin(regions: Region[]): Region[] {
    return [...regions].sort((a, b) => {
        const letterA = getFirstLetter(a.pinyin)
        const letterB = getFirstLetter(b.pinyin)
        return letterA.localeCompare(letterB)
    })
}

function convertFirstLevelRegionsToOptions(regions: Region[]) {
    const sortedRegions = sortRegionsByPinyin(regions)
    return sortedRegions.map((region) => ({
        value: region.value,
        label: region.label,
        children: region.children,
    }))
}
const onChange = ({ value, tabIndex }: { value: string | number; tabIndex: number }) => {
    if (tabIndex === 0) {
        const targetIndex = cascaderOptions.value.findIndex((e) => e.value === value)

        if (targetIndex === -1) return

        tab1Index.value = targetIndex
        const province: Region = JSON.parse(JSON.stringify(cascaderOptions.value[targetIndex]))

        if (province.children) {
            const temp = convertFirstLevelRegionsToOptions(province.children || []) as Option[]

            temp.unshift({
                value: province.value,
                label: '全部',
            })

            cascaderOptions.value[targetIndex].children = temp
        }
    }

    if (tabIndex === 1) {
        const province = cascaderOptions.value[tab1Index.value].children || []
        const targetIndex = province.findIndex((e) => e.value === value)

        if (targetIndex === -1) return
        tab2Index.value = targetIndex

        const city: Region = JSON.parse(JSON.stringify(province[targetIndex]))

        if (city.children) {
            const temp = convertFirstLevelRegionsToOptions(city.children || []) as Option[]

            temp.unshift({
                value: city.value,
                label: '全部',
            })

            province[targetIndex].children = cleanChildren(temp) as Option[]
        }
    }
}

const onFinish = ({
    value,
    selectedOptions,
    tabIndex,
}: {
    value: string | number
    selectedOptions: CascaderOption[]
    tabIndex: number
}) => {
    const options = selectedOptions as Region[]

    if (tabIndex === 2) {
        const parentValue = options[1]?.value
        if (parentValue === value) {
            return onFinish({ value, selectedOptions, tabIndex: tabIndex - 1 })
        }

        const params = {
            ...options[2],
            parent: { ...options[1], parent: options[0] },
        }
        props.pushSelectRegion(params)
    }

    if (tabIndex === 1) {
        const parentValue = options[0]?.value
        if (parentValue === value) {
            return onFinish({ value, selectedOptions, tabIndex: tabIndex - 1 })
        }

        const params = {
            ...options[1],
            parent: options[0],
        }

        props.pushSelectRegion(params)
    }
    if (tabIndex === 0) {
        const params = {
            ...options[0],
        }
        props.pushSelectRegion(params)
    }

    console.log('关闭')
    props.close()
}

const onClose = () => {
    console.log('close')
    props.close()
}

watch(
    () => props.regions,
    (value) => {
        console.log('value', value)
        cascaderOptions.value = formatOptions(value)
    }
)

onMounted(() => {
    cascaderOptions.value = formatOptions(props.regions)
})
</script>

<template>
    <van-cascader
        v-model="cascaderValue"
        title="请选择所在地区"
        :options="cascaderOptions"
        :field-names="fieldNames"
        @change="onChange"
        @finish="onFinish"
        @close="onClose"
    />
</template>

<style lang="scss" scoped></style>
