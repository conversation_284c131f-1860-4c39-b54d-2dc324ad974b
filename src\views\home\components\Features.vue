<script lang="ts" setup>
import IMG01 from '@/assets/hub-images/home/<USER>'
import IMG02 from '@/assets/hub-images/home/<USER>'
import IMG03 from '@/assets/hub-images/home/<USER>'
import { useRouter } from 'vue-router'
const router = useRouter()

const list = [
    {
        title: '产业市场',
        img: IMG02,
        action: () => {
            router.push({
                name: 'industry-market',
            })
        },
    },
    {
        title: '地图查找',
        img: IMG03,
        action: () => {
            router.push({
                name: 'mapSearch',
            })
        },
    },
    {
        title: '企业体检',
        img: IMG01,
        action: () => {
            window.open('https://jyhy.shuzutech.com')
        },
    },
]
</script>

<template>
    <div class="home-features">
        <div class="flex flex-row gap-32">
            <div
                class="flex flex-column left-right-center top-bottom-center gap-8"
                v-for="(item, index) in list"
                :key="index"
                @click="item.action"
            >
                <div class="flex w-52 h-52 border-radius-52 back-color-white left-right-center top-bottom-center">
                    <img :src="item.img" alt="" class="w-30 h-30" />
                </div>
                <div class="font-12 color-black font-weight-500">{{ item.title }}</div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.home-features {
    margin-bottom: 16px;
    padding-left: 16px;
    padding-right: 16px;
}
</style>
