<template>
    <div class="home tb-padding-12 lr-padding-16 border-box" style="background-color: #F2F5F8">
        <div class="font-16">
            以下是
            <span class="color-blue">{{ route.query.name }}</span>
            模板企业
        </div>
        <div>
            <CompanySearchList
                :load-data="getData"
                :refresh-data="refreshData"
                :loading="loading"
                :refreshing="refreshing"
                :data="list"
                :total="pageInfo.total"
                :finished="finished"
            />
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, computed, onMounted, watch } from 'vue'
import type { Ref } from 'vue'
import { tabbarheight } from '@/utils/tabbar-height'
import CompanySearchList from '@/components/enterprise/list/CompanySearchList.vue'
import { useRoute } from 'vue-router'
import type { ICompanyInfo } from '@/types/company'
import aicService from '@/service/aicService'
import type { conditionItem, ISearchGetTemplateItem } from '@/types/company' 
import { useStore } from 'vuex'
import type { IHighSearchRules, ISearchConditions } from '@/types/model'
import { showFailToast } from 'vant'
import { mergeList } from '@/utils/merge-list'

const store = useStore()
const activeTemplete: Ref<ISearchGetTemplateItem | null> = ref(null)
const route = useRoute()
const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})
const error = ref(false)
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const list = ref<ICompanyInfo[]>([])
const searchchannellype = ref(0)
const initial = ref(false)
const pageInfo = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    realTotal: 0,
})

const refreshData = () => {
    pageInfo.value.page = 1
    getData('refresh')
}

const getData = (action?: 'refresh' | 'reload' | 'switch') => {
    if (refreshing.value || loading.value) return

    if (action === 'refresh') {
        refreshing.value = true
        pageInfo.value.page = 1
        pageInfo.value.total = 0
        pageInfo.value.realTotal = 0
    } else if (action === 'reload') {
        finished.value = false
        loading.value = true
        pageInfo.value.page = 1
        pageInfo.value.total = 0
        pageInfo.value.realTotal = 0
        list.value = []
    } else {
        finished.value = false
        loading.value = true
    }

    aicService
        .searchAdvancedSearch({
            condition.value, 
            page: pageInfo.value.page, 
            pageSize: pageInfo.value.pageSize, 
            sortBy: 0
        })
        .then((res) => {
            loading.value = false
            refreshing.value = false
            const { errCode, data, total, channelType, realTotal, errMsg, lastPage } = res
            if (errCode === 0) {
                error.value = false
                list.value = mergeList(list.value, data, 'id')
                pageInfo.value.total = total
                pageInfo.value.realTotal = realTotal
                searchchannellype.value = channelType

                if (!lastPage) pageInfo.value.page += 1
                finished.value = list.value.length >= total

                if (total === 0) {
                    list.value = []
                }
            } else {
                error.value = true
                showFailToast(errMsg || '查询失败')
            }
        })
        .catch((error) => {
            const { isCanceled } = error
            if (!isCanceled) {
                refreshing.value = false
                loading.value = false
                error.value = true
            }
        })
        .finally(() => {
            refreshing.value = false
            loading.value = false
            initial.value = true
        })
}

const getTemplateById = () => {
    //根据id获取指定模板
    aicService
        .searchGetTemplate({
            templateId: route.query.templateId as string,
            searchType: '1',
            page: 1,
            pageSize: 20,
        })
        .then((res) => {
            if (res.data.length) {
                activeTemplete.value = res.data[0]
                // console.log('activeTemplete', activeTemplete.value)
            }
        })
}

const trSearchRules = (data: ISearchConditions) => {
    const cacheHighSearchRulesData = store.state.app.hightSearchRulesData
    let traverse = (nodes: ISearchConditions[]) => {
        let res = nodes.map((node) => {
            let result: conditionItem = {
                cn: '',
                cr: '',
                cv: [],
            }

            console.log(node)

            let dataType = node.dataType

            let operator = ['number', 'date'].includes(dataType || '') ? 'BETWEEN' : node.operator

            console.log('dataType', dataType, operator)
            if (node.children && node.children.length) {
                result.cn = 'composite'
                result.cr = operator
                result.cv = traverse(node.children)
            } else {
                result.cn = node.prop
                result.cr = operator

                if (node.prop === 'industry') {
                    // console.log('1')
                    result.cv = node.valueLabel || []
                } else {
                    // console.log('2')
                    console.log(node.value)
                    let nValue = JSON.parse(JSON.stringify(node.value))
                    if (dataType === 'select') {
                        if (nValue === '1') {
                            nValue = true
                        } else {
                            nValue = false
                        }
                        //将单选值转换成布尔值
                    } else if (operator === 'BETWEEN') {
                        nValue = [`${nValue[0]}-${nValue[1]}`]
                    }
                    if (Array.isArray(nValue)) {
                        // console.log('3')
                        if (dataType === 'mapped' || node.prop === 'area') {
                            console.log('is mapped or prop is area')
                            cacheHighSearchRulesData.forEach((group: IHighSearchRules) => {
                                if (group.children) {
                                    let res = group.children.find((item) => {
                                        return item.key === node.prop
                                    })
                                    if (res && res.levelConfig) {
                                        result.cv = {}
                                        res.levelConfig.levels.forEach(
                                            (level: { name: string; title: string }, idx: number) => {
                                                console.log('nValue', nValue)
                                                let nowLevelVal = nValue.filter((vItem) => {
                                                    return vItem.length === idx + 1
                                                })
                                                if (nowLevelVal.length) {
                                                    let valArr = nowLevelVal
                                                        .map((i) => {
                                                            return i[idx]
                                                        })
                                                        .flat()
                                                    ;(result.cv as { [key: string]: unknown })[level.name] = [
                                                        ...new Set(valArr),
                                                    ]
                                                }
                                            }
                                        )
                                    }
                                }
                            })
                        } else {
                            // console.log('4')
                            // console.log('dataType',dataType )
                            // console.log('nValue',nValue)
                            // console.log('operator',operator)
                            if ((operator === 'IN' || operator === 'NOT_IN') && dataType === 'dateRangeMultiSelect') {
                                nValue = [`${nValue[0]}-${nValue[1]}`]
                                result.cv = nValue
                            } else {
                                result.cv = [...new Set(nValue.flat())]
                            }
                        }
                    } else {
                        result.cv = nValue
                    }
                }
            }
            return result
        })

        return res
    }

    // 从根节点开始转换
    return {
        cn: 'composite',
        cr: 'MUST',
        cv: traverse(data.children || []),
    }
}
const condition = ref<conditionItem>()
watch(() =>activeTemplete.value, () => {
    if (activeTemplete.value) {
        const res = trSearchRules(activeTemplete.value.searchData.list[0])
        condition.value = res
        console.log('res12312312', res)
    }
})

onMounted(async () => {
    await getTemplateById()
})

</script>

<style lang='scss' scoped>
.home {
    height: 100%;
    overflow: scroll;
    padding-bottom: v-bind(paddingBottom);
    display: flex;
    flex-direction: column;
}
</style>