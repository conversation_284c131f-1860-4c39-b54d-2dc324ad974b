<template>
    <div class="all-padding-20">
        <div v-if="isLock === 1" class="font-16 text-center t-margin-100">暂无权限查看~</div>
        <div v-else>
            <div
                v-for="(group, groupIndex) in groupedColumns"
                :key="groupIndex"
                class="row-group"
                :class="{ 'has-multiple-items': group.length > 1 }"
            >
                <div v-for="item in group" :key="item.key" class="item font-16">
                    <div class="label color-two-grey">{{ item.name }}</div>
                    <div
                        v-if="
                            [ColumnType.RelateCompanyLink, ColumnType.CompanyProfileLink].includes(item.type) &&
                            getValue(item.key)
                        "
                        class="flex gap-5 top-bottom-center"
                    >
                        <div
                            v-if="item.key === 'legalperson' && page_config.key === RequestKeys.GSInfo"
                            class="w-30 h-30 text-center top-bottom-center border-radius-4 lh-30 color-white"
                            style="background-color: #d1ae9d"
                        >
                            {{ getValue(item.key)?.split('')[0] }}
                        </div>
                        <RelateLink
                            :data="info"
                            :channel-type="info.channelType"
                            :name="getValue(item.key)"
                            :count="info['personRelatedEntNum'] && Number(info['personRelatedEntNum'])"
                            :model-name="props.modelName"
                        />
                    </div>

                    <IText
                        v-else
                        :content="item.render ? item.render(info) : getValue(item.key)"
                        :max-lines="item.textCollapse ? 3 : 999999"
                        :defaultExpanded="item.textCollapse || false"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { PageConfig, ColumnType, RequestKeys } from '../config'
import IText from './IText.vue'
import aicService from '@/service/aicService'
import RelateLink from './RelateLink.vue'
import { getNestedValue } from '../utils'

const props = defineProps<{
    modelName: keyof typeof PageConfig
    socialCreditCode: string
}>()
const CURRENT_PAGE = ref(props.modelName)

const page_config = computed(() => {
    return PageConfig[CURRENT_PAGE.value]
})

const isLock = ref<number>(0)

// 将columns按groupIndex分组
const groupedColumns = computed(() => {
    const columns = page_config.value.columns || []
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const groups: Record<string, any[]> = {}

    // 对columns进行分组
    columns.forEach((column) => {
        const groupKey = column.groupIndex !== undefined ? `group_${column.groupIndex}` : `single_${column.key}`
        if (!groups[groupKey]) {
            groups[groupKey] = []
        }
        groups[groupKey].push(column)
    })

    // 将分组转换为数组
    return Object.values(groups)
})
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const info = ref<any>({})
const getData = () => {
    aicService
        .gsInfo({
            modelName: CURRENT_PAGE.value,
            socialCreditCode: props.socialCreditCode,
        })
        .then((res) => {
            console.log(res)
            info.value = res
            isLock.value = res.isLock
        })
}

const getValue = (key: string) => {
    return getNestedValue(info.value, key)
}

onMounted(() => {
    getData()
})
</script>
<style lang="scss" scoped>
.row-group {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px;
    gap: 30px;
    position: relative;
    &::before {
        content: ' ';
        width: 1px;
        height: 100%;
        position: absolute;
        left: 50%;
        top: 0;
        background-color: #e5e5e5;
        display: none; // 默认不显示
    }
}

// 当分组中有多个元素时才显示伪元素
.row-group.has-multiple-items {
    &::before {
        display: block;
    }
}

.item {
    flex: 1;
}

.label {
    margin-bottom: 5px;
}

.value {
    word-break: break-all;
}
</style>
