<script lang="ts" setup>
import { getSearchHistory } from '@/utils/history'
import { onMounted, ref } from 'vue'
const historyList = ref<string[]>([])

const props = defineProps<{
    onPick: (v: string) => void
}>()

onMounted(() => {
    const history = getSearchHistory()
    historyList.value = history
})

const onClickPage = () => {
    console.log('onClickPage')
}

const onClickItem = (item: string) => {
    props.onPick(item)
}
</script>

<template>
    <div class="flex flex-column gap-16 all-padding-12 height-100" @click="onClickPage">
        <div class="flex flex-row space-between font-14 width-100">
            <div class="color-black" v-if="historyList.length > 0">最近搜索</div>
            <div class="color-text-grey" v-if="historyList.length > 0">清除记录</div>
        </div>
        <div class="flex fex-grow gap-12 flex-wrap">
            <div
                class="font-14 lr-padding-12 tb-padding-4 back-color-border border-radius-6"
                v-for="value in historyList"
                @click.stop="onClickItem(value)"
                :key="value"
            >
                {{ value }}
            </div>
        </div>
    </div>
</template>

<style scoped></style>
