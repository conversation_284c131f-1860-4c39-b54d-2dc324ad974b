<script lang="ts" setup>
import POSTER01 from '@/assets/hub-images/home/<USER>'

const list = [
    {
        img: POSTER01,
        action: () => {
            window.open('https://jyhy.shuzutech.com')
        },
    },
]
</script>

<template>
    <div class="home-swipe">
        <van-swipe class="home-van-swipe" :autoplay="3000" indicator-color="white">
            <van-swipe-item :autoplay="3000" v-for="(item, index) in list" :key="index">
                <img :src="item.img" class="width-100 height-100 img-cover" @click="item.action()" />
            </van-swipe-item>
        </van-swipe>
    </div>
</template>

<style scoped>
.home-swipe {
    height: 141px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 12px;
    padding-left: 16px;
    padding-right: 16px;
}

.home-van-swipe {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 8px;
}
</style>
