<template>
    <div style="background-color: #f1f3f7; min-height: 100vh" ref="scrollContainer" @scroll="handleContainerScroll">
        <div
            class="b-margin-10 all-padding-15 flex flex-column gap-6 font-16"
            style="background-color: #fff"
            v-for="(companyBaseInfo, index) in list"
            :key="index"
            @click="handleToCompanyDetail(companyBaseInfo)"
        >
            <div class="flex top-bottom-center gap-6">
                <div class="w-50 h-50 border-radius-10" style="background-color: #e9f3fb"></div>
                <div class="flex flex-column gap-6">
                    <div class="font-16 font-weight-700">{{ companyBaseInfo.entName }}</div>
                    <div class="flex gap-8 top-bottom-center">
                        <div
                            v-for="(item, index) in companyBaseInfo.entTags.slice(
                                0,
                                companyBaseInfo.entTags.length > 4 ? 3 : 4
                            )"
                            :key="index"
                            :class="
                                item.categoryCode == '001' ? 'green' : item.categoryCode === '002' ? 'yellow' : 'blue'
                            "
                            class="cus-tag"
                        >
                            {{ item.tagName }}
                        </div>
                        <div v-if="companyBaseInfo.entTags.length > 4" class="cus-tag blue font-weight-700">...</div>
                    </div>
                </div>
            </div>
            <div class="font-14" style="display: flex; align-items: center; gap: 4px">
                <!-- 使用计算属性动态生成带条件分隔符的内容 -->
                <template v-for="(item, index) in validInfoItems(companyBaseInfo)" :key="index">
                    <span v-if="item.value" :style="item.style">{{ item.value }}</span>
                    <span v-if="index < validInfoItems(companyBaseInfo).length - 1" class="separator">|</span>
                </template>
            </div>
            <div class="font-14">{{ companyBaseInfo.position }}</div>
        </div>
        <div ref="loadTrigger" class="loading-trigger" v-if="hasMoreData">
            <span>{{ loading ? '正在加载中...' : '上拉加载更多' }}</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import router from '@/router'
import aicService from '@/service/aicService'
import type { PersonEnterpriseRelationsItem } from '@/types/aic'
import { showFailToast } from 'vant'
import { computed, onMounted, onUnmounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const entId = route.query.entId as string
const name = route.query.name as string

const list = reactive<PersonEnterpriseRelationsItem[]>([])
const paginationConfig = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
})
const isLoadMore = ref(false)

const loading = ref(false)
// 添加滚动加载相关变量
const loadTrigger = ref<HTMLDivElement | null>(null)
const scrollContainer = ref<HTMLDivElement | null>(null)

// 计算是否还有更多数据
const hasMoreData = computed(() => {
    return paginationConfig.page * paginationConfig.pageSize < paginationConfig.total
})

// 获取关联企业列表
const getRelateCompanyList = async () => {
    if (!entId) {
        return
    }
    loading.value = true
    try {
        const res = await aicService.gsGetPersonEnterpriseRelations({
            entId,
            page: paginationConfig.page,
            name,
            companyName: '',
        })
        if (res && res.data && 'items' in res.data && Array.isArray(res.data.items)) {
            // 更新总数
            paginationConfig.total = res?.data?.total || 0

            // 如果是加载更多，则将新数据追加到现有列表
            if (isLoadMore.value) {
                list.push(...res.data.items)
            } else {
                // 否则替换整个列表
                list.length = 0
                list.push(...res.data.items)
            }
            loading.value = false
        }
    } catch (error) {
        console.error('获取列表数据失败：', error)
        loading.value = false
    }
}
const loadMore = () => {
    paginationConfig.page += 1
    isLoadMore.value = true
    getRelateCompanyList()
}
const validInfoItems = (companyBaseInfo: PersonEnterpriseRelationsItem) => {
    // 收集所有可能的信息项
    const items = [
        { value: name, style: { color: 'var(--main-blue-)' } },
        { value: companyBaseInfo?.regCapital },
        { value: companyBaseInfo?.positionStatus },
    ]
    // 过滤掉空值项并返回
    return items.filter((item) => item.value !== undefined && item.value !== null && item.value !== '')
}
// 处理容器滚动事件
const handleContainerScroll = () => {
    if (loading.value || !hasMoreData.value) return

    if (scrollContainer.value && loadTrigger.value) {
        const containerRect = scrollContainer.value.getBoundingClientRect()
        const triggerRect = loadTrigger.value.getBoundingClientRect()

        // 当加载触发器进入视口时加载更多
        if (triggerRect.top < containerRect.bottom + 100) {
            console.log('容器滚动触发加载更多')
            loadMore()
        }
    }
}

// 处理全局滚动事件
const handleWindowScroll = () => {
    if (!loading.value && hasMoreData.value && loadTrigger.value) {
        const rect = loadTrigger.value.getBoundingClientRect()
        if (rect.top < window.innerHeight + 100) {
            console.log('全局滚动触发加载更多')
            loadMore()
        }
    }
}
const toCompanyDetail = (companyName: string) => {
    if (!companyName) {
        showFailToast({ message: '数据错误' })
        return
    }

    aicService
        .searchEnterprise({
            keyword: companyName,
            scope: 'companyname',
            pageSize: 1,
            page: 1,
        })
        .then((res) => {
            const company = res.data[0]
            if (company?.companyName === companyName) {
                router.push({
                    name: 'companyDetail',
                    query: { socialCreditCode: company.socialCreditCode },
                })
            } else {
                showFailToast({ message: '未找到该公司' })
            }
        })
}
const handleToCompanyDetail = (info: PersonEnterpriseRelationsItem) => {
    toCompanyDetail(info.entName)
}

onMounted(() => {
    getRelateCompanyList()

    // 添加全局滚动监听
    setTimeout(() => {
        // 使用window的滚动事件作为备份
        window.addEventListener('scroll', handleWindowScroll)
        console.log('滚动监听已添加')
    }, 500)
})

onUnmounted(() => {
    // 清理滚动监听
    window.removeEventListener('scroll', handleWindowScroll)
})
</script>

<style scoped lang="scss">
.cus-tag {
    padding: 4px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 12px;
    flex-shrink: 0;
}
.green {
    background-color: rgba(38, 201, 123, 0.15);
    color: #26c96a;
}
.yellow {
    background-color: rgba(236, 171, 40, 0.15);
    color: #ecab28;
}
.blue {
    background-color: rgba(43, 131, 253, 0.15);
    color: #4f83fd;
}
.loading-trigger {
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    margin-top: 10px;
    color: #999;
    font-size: 14px;
    padding: 10px 0;
    border-top: 1px solid #eee;
}
</style>
