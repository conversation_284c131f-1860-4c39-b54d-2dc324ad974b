<script lang="ts" setup>
import { computed, ref, watch } from 'vue'
import BasicSearchItem from './BasicSearchItem.vue'
import type { ICompanyInfo } from '@/types/company'
import { showConfirmDialog, showToast, type ActionSheetAction } from 'vant'
import crmService from '@/service/crmService'
const props = defineProps<{
    refreshing: boolean
    loading: boolean
    loadData?: () => void
    refreshData?: () => void
    total?: number
    data?: ICompanyInfo[]
    scope?: string
    finished?: boolean
}>()

const currentCompany = ref<ICompanyInfo | null>(null)
// const finished = ref(false)
const error = ref(false)
const showActionSheet = ref(false)
const actions = [{ name: '转线索' }, { name: '转客户' }]
const list = ref<ICompanyInfo[] | null>(null)
const loadingRef = ref(false)
const refreshingRef = ref(false)

const actionDescription = computed(() => {
    const { companyName } = currentCompany.value || {}
    return `${companyName}`
})

const showEmpty = computed(() => {
    return list.value !== null && list.value.length === 0 && props.finished
})

const showTotal = computed(() => {
    return list.value !== null && list.value.length > 0
})

const openActionSheet = (data: ICompanyInfo) => {
    currentCompany.value = data
    showActionSheet.value = true
}

const onActionSheetSelect = (_: ActionSheetAction, index: number) => {
    let clueType = 2
    let title = '转线索'
    if (index === 2) {
        clueType = 3
        title = '转客户'
    }

    showConfirmDialog({
        title: title,
        message: `${title}将会扣除线索权益额度，请确认是否继续？`,
    })
        .then(() => {
            doTransfer(clueType)
        })
        .catch(() => {})
}

const doTransfer = (clueType: number) => {
    if (!currentCompany.value) return
    crmService
        .crmAdd({
            clueType: clueType,
            socialCreditCode: currentCompany.value.socialCreditCode,
            companyName: currentCompany.value.companyName,
            source: 1,
        })
        .then((res) => {
            const { errCode, errMsg } = res
            if (errCode === 0) {
                showToast('转移成功')
                updateItemStatus()
            } else if (errCode === 500 && errMsg.includes('已存在')) {
                updateItemStatus()
            } else {
                showToast('转移失败')
            }
        })
}

const updateItemStatus = () => {
    if (!list.value) return
    const targetIndex = list.value.findIndex((e) => e.id === currentCompany.value?.id)
    if (targetIndex === -1) return
    list.value[targetIndex].isBuy = true
}

watch(
    () => props.data,
    (value) => {
        list.value = value || []
    },
    { deep: true }
)

watch(
    () => props.loading,
    (value) => {
        loadingRef.value = value
    }
)

watch(
    () => props.refreshing,
    (value) => {
        refreshingRef.value = value
    }
)
</script>

<template>
    <van-pull-refresh v-model="refreshingRef" @refresh="refreshData" class="min-height-100">
        <van-list
            v-model:loading="loadingRef"
            v-model:error="error"
            :finished="finished"
            finished-text="没有更多了"
            @load="loadData"
            error-text="请求失败，点击重新加载"
            :loading-text="refreshingRef ? ' ' : '查询中...'"
            v-if="!showEmpty"
        >
            <div class="flex flex-column all-padding-12 gap-8">
                <div class="font-14 font-weight-500 color-text-grey" v-if="showTotal">
                    找到 <span class="color-blue">{{ total }}</span> 个结果
                </div>
                <BasicSearchItem
                    v-for="item in list"
                    :key="item.id"
                    :data="item"
                    :openActionSheet="openActionSheet"
                    :scope="scope || '0'"
                />
            </div>
        </van-list>
        <van-empty
            v-if="showEmpty"
            image="https://fastly.jsdelivr.net/npm/@vant/assets/custom-empty-image.png"
            image-size="80"
            description="未查询到相关数据"
        />
    </van-pull-refresh>
    <van-action-sheet
        v-model:show="showActionSheet"
        :actions="actions"
        cancel-text="取消"
        :description="actionDescription"
        close-on-click-action
        @select="onActionSheetSelect"
    />
</template>

<style scoped></style>
